# Pri Fashion System - Database Schema Documentation

## Entity Relationship Overview

The Pri Fashion system database consists of 16 main entities organized into 5 functional groups:

### 1. Authentication & User Management
- **Role**: User role definitions (owner, inventory_manager, sales_team, order_coordinator)
- **CustomUser**: Extended Django user model with role-based access control

### 2. Supplier & Fabric Management
- **Supplier**: Fabric supplier information and contact details
- **FabricDefinition**: Fabric types and specifications from suppliers
- **FabricVariant**: Color variants of fabrics with inventory tracking

### 3. Production Workflow
- **CuttingRecord**: Cutting session records and product information
- **CuttingRecordFabric**: Detailed fabric usage per cutting session
- **DailySewingRecord**: Daily sewing progress tracking by size
- **FinishedProduct**: Completed products ready for approval and sale

### 4. Product & Inventory Management
- **ProductImage**: Multiple image support for finished products
- **PackingSession**: Individual packing activity records
- **PackingInventory**: Current stock levels for packed products

### 5. Order & Sales Management
- **Shop**: Customer shop information with location data
- **Order**: Customer orders with comprehensive tracking
- **OrderItem**: Individual line items within orders
- **Payment**: Multiple payment records supporting split payments

## Key Relationships

### One-to-One Relationships
- CuttingRecord ↔ FinishedProduct (Each cutting batch produces one finished product)
- FinishedProduct ↔ PackingInventory (Each product has one inventory record)

### One-to-Many Relationships
- Role → CustomUser (Users have one role)
- Supplier → FabricDefinition (Suppliers provide multiple fabrics)
- FabricDefinition → FabricVariant (Fabrics have multiple color variants)
- CuttingRecord → CuttingRecordFabric (Cutting sessions use multiple fabrics)
- CuttingRecordFabric → DailySewingRecord (Fabric records have daily sewing entries)
- FinishedProduct → ProductImage (Products can have multiple images)
- FinishedProduct → PackingSession (Products have multiple packing sessions)
- Shop → Order (Shops place multiple orders)
- CustomUser → Order (Users create multiple orders)
- Order → OrderItem (Orders contain multiple items)
- Order → Payment (Orders can have multiple payments)

### Many-to-Many Relationships (via Junction Tables)
- FabricVariant ↔ CuttingRecord (via CuttingRecordFabric)
- FinishedProduct ↔ Order (via OrderItem)

## Data Integrity Features

### Constraints
- Unique constraints on Role.name and CustomUser.username
- Foreign key constraints maintain referential integrity
- Cascade deletions where appropriate (e.g., Order → OrderItem)
- Protected deletions for critical references (e.g., FinishedProduct in OrderItem)

### Validation
- Fabric availability validation before cutting
- Stock level validation before order processing
- Payment amount validation against order totals
- Size-based quantity tracking (XS, S, M, L, XL)

### Audit Trail
- Complete traceability from fabric purchase to order delivery
- Timestamp tracking for all major operations
- User attribution for all transactions
- Status tracking throughout the production workflow

## Business Logic Support

### Production Flow
1. Fabric procurement and variant management
2. Cutting record creation with fabric allocation
3. Daily sewing progress tracking
4. Finished product approval and pricing
5. Packing and inventory management
6. Order processing and fulfillment

### Financial Tracking
- Manufacturing cost calculation
- Selling price management
- Multiple payment method support
- Credit terms and due date tracking
- Payment history and reconciliation

### Inventory Management
- Real-time fabric availability tracking
- Finished product stock levels
- Packing configuration (6-packs, 12-packs, extra items)
- Automatic quantity calculations and updates

This schema supports all 21 functional requirements of the Pri Fashion system while maintaining data integrity, scalability, and comprehensive audit capabilities.
