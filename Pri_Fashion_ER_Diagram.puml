@startuml Pri_Fashion_ER_Diagram

!define ENTITY entity
!define PK <color:red><b>PK</b></color>
!define FK <color:blue><b>FK</b></color>
!define UK <color:green><b>UK</b></color>

title Pri Fashion System - Entity Relationship Diagram

' Authentication and User Management
ENTITY Role {
  PK id : int
  UK name : string
}

ENTITY CustomUser {
  PK id : int
  UK username : string
  first_name : string
  last_name : string
  email : string
  password : string
  date_joined : datetime
  is_active : boolean
  FK role_id : int
}

' Supplier Management
ENTITY Supplier {
  PK id : int
  name : string
  contact_person : string
  phone_number : string
  email : string
  address : text
  date_added : date
}

' Fabric Management
ENTITY FabricDefinition {
  PK id : int
  fabric_name : string
  FK supplier_id : int
  date_added : date
}

ENTITY FabricVariant {
  PK id : int
  FK fabric_definition_id : int
  color : string
  color_name : string
  total_yard : float
  available_yard : float
  price_per_yard : float
}

' Cutting Management
ENTITY CuttingRecord {
  PK id : int
  cutting_date : date
  description : text
  product_name : string
}

ENTITY CuttingRecordFabric {
  PK id : int
  FK cutting_record_id : int
  FK fabric_variant_id : int
  yard_usage : decimal
  xs : int
  s : int
  m : int
  l : int
  xl : int
}

' Sewing Management
ENTITY DailySewingRecord {
  PK id : int
  FK cutting_record_fabric_id : int
  date : date
  xs : int
  s : int
  m : int
  l : int
  xl : int
  damage_count : int
}

' Finished Product Management
ENTITY FinishedProduct {
  PK id : int
  FK cutting_record_id : int <<OneToOne>>
  total_sewn_xs : int
  total_sewn_s : int
  total_sewn_m : int
  total_sewn_l : int
  total_sewn_xl : int
  manufacture_price : float
  selling_price : float
  approval_date : date
  is_provisional : boolean
  available_quantity : int
  product_image : image
  notes : text
}

ENTITY ProductImage {
  PK id : int
  FK finished_product_id : int
  image : image
  external_url : string
  order : int
}

' Packing Management
ENTITY PackingSession {
  PK id : int
  FK finished_product_id : int
  date : date
  number_of_6_packs : int
  number_of_12_packs : int
  extra_items : int
}

ENTITY PackingInventory {
  PK id : int
  FK finished_product_id : int <<OneToOne>>
  number_of_6_packs : int
  number_of_12_packs : int
  extra_items : int
}

' Order Management
ENTITY Shop {
  PK id : int
  name : string
  address : text
  contact_number : string
  district : string
  latitude : float
  longitude : float
}

ENTITY Order {
  PK id : int
  FK shop_id : int
  FK placed_by_id : int
  created_at : datetime
  status : string
  approval_date : datetime
  invoice_number : string
  delivery_date : datetime
  delivered_items_count : int
  delivery_notes : text
  payment_method : string
  payment_status : string
  amount_paid : decimal
  payment_date : datetime
  check_number : string
  check_date : date
  bank_name : string
  payment_due_date : date
  credit_term_months : int
  owner_notes : text
}

ENTITY OrderItem {
  PK id : int
  FK order_id : int
  FK finished_product_id : int
  quantity_6_packs : int
  quantity_12_packs : int
  quantity_extra_items : int
}

ENTITY Payment {
  PK id : int
  FK order_id : int
  amount : decimal
  payment_method : string
  payment_date : datetime
  notes : text
  check_number : string
  check_date : date
  bank_name : string
  credit_term_months : int
  payment_due_date : date
}

' Relationships
Role ||--o{ CustomUser : "has role"

Supplier ||--o{ FabricDefinition : "supplies"

FabricDefinition ||--o{ FabricVariant : "has variants"

CuttingRecord ||--o{ CuttingRecordFabric : "contains"
FabricVariant ||--o{ CuttingRecordFabric : "used in"

CuttingRecordFabric ||--o{ DailySewingRecord : "sewn daily"

CuttingRecord ||--|| FinishedProduct : "produces"
FinishedProduct ||--o{ ProductImage : "has images"

FinishedProduct ||--o{ PackingSession : "packed in"
FinishedProduct ||--|| PackingInventory : "tracked in"

Shop ||--o{ Order : "places"
CustomUser ||--o{ Order : "created by"

Order ||--o{ OrderItem : "contains"
FinishedProduct ||--o{ OrderItem : "ordered as"

Order ||--o{ Payment : "paid through"

@enduml
