Stack trace:
Frame         Function      Args
0007FFFFAEF0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9DF0) msys-2.0.dll+0x1FE8E
0007FFFFAEF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB1C8) msys-2.0.dll+0x67F9
0007FFFFAEF0  000210046832 (000210286019, 0007FFFFADA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAEF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAEF0  000210068E24 (0007FFFFAF00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB1D0  00021006A225 (0007FFFFAF00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFABD8A0000 ntdll.dll
7FFABC2C0000 KERNEL32.DLL
7FFABACA0000 KERNELBASE.dll
7FFABB780000 USER32.dll
7FFABB560000 win32u.dll
7FFABBD40000 GDI32.dll
7FFABB090000 gdi32full.dll
7FFABB590000 msvcp_win.dll
7FFABB290000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFABD490000 advapi32.dll
7FFABBEA0000 msvcrt.dll
7FFABC020000 sechost.dll
7FFABBD70000 RPCRT4.dll
7FFABA110000 CRYPTBASE.DLL
7FFABAA80000 bcryptPrimitives.dll
7FFABBF50000 IMM32.DLL
