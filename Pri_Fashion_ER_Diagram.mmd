erDiagram
    %% Authentication and User Management
    Role {
        int id PK
        string name UK "unique"
    }
    
    CustomUser {
        int id PK
        string username UK "unique"
        string first_name
        string last_name
        string email
        string password
        datetime date_joined
        boolean is_active
        int role_id FK
    }
    
    %% Supplier Management
    Supplier {
        int id PK
        string name
        string contact_person
        string phone_number
        string email
        text address
        date date_added
    }
    
    %% Fabric Management
    FabricDefinition {
        int id PK
        string fabric_name
        int supplier_id FK
        date date_added
    }
    
    FabricVariant {
        int id PK
        int fabric_definition_id FK
        string color
        string color_name
        float total_yard
        float available_yard
        float price_per_yard
    }
    
    %% Cutting Management
    CuttingRecord {
        int id PK
        date cutting_date
        text description
        string product_name
    }
    
    CuttingRecordFabric {
        int id PK
        int cutting_record_id FK
        int fabric_variant_id FK
        decimal yard_usage
        int xs
        int s
        int m
        int l
        int xl
    }
    
    %% Sewing Management
    DailySewingRecord {
        int id PK
        int cutting_record_fabric_id FK
        date date
        int xs
        int s
        int m
        int l
        int xl
        int damage_count
    }
    
    %% Finished Product Management
    FinishedProduct {
        int id PK
        int cutting_record_id FK "OneToOne"
        int total_sewn_xs
        int total_sewn_s
        int total_sewn_m
        int total_sewn_l
        int total_sewn_xl
        float manufacture_price
        float selling_price
        date approval_date
        boolean is_provisional
        int available_quantity
        image product_image
        text notes
    }
    
    ProductImage {
        int id PK
        int finished_product_id FK
        image image
        string external_url
        int order
    }
    
    %% Packing Management
    PackingSession {
        int id PK
        int finished_product_id FK
        date date
        int number_of_6_packs
        int number_of_12_packs
        int extra_items
    }
    
    PackingInventory {
        int id PK
        int finished_product_id FK "OneToOne"
        int number_of_6_packs
        int number_of_12_packs
        int extra_items
    }
    
    %% Order Management
    Shop {
        int id PK
        string name
        text address
        string contact_number
        string district
        float latitude
        float longitude
    }
    
    Order {
        int id PK
        int shop_id FK
        int placed_by_id FK
        datetime created_at
        string status
        datetime approval_date
        string invoice_number
        datetime delivery_date
        int delivered_items_count
        text delivery_notes
        string payment_method
        string payment_status
        decimal amount_paid
        datetime payment_date
        string check_number
        date check_date
        string bank_name
        date payment_due_date
        int credit_term_months
        text owner_notes
    }
    
    OrderItem {
        int id PK
        int order_id FK
        int finished_product_id FK
        int quantity_6_packs
        int quantity_12_packs
        int quantity_extra_items
    }
    
    Payment {
        int id PK
        int order_id FK
        decimal amount
        string payment_method
        datetime payment_date
        text notes
        string check_number
        date check_date
        string bank_name
        int credit_term_months
        date payment_due_date
    }
    
    %% Relationships
    Role ||--o{ CustomUser : "has role"
    
    Supplier ||--o{ FabricDefinition : "supplies"
    
    FabricDefinition ||--o{ FabricVariant : "has variants"
    
    CuttingRecord ||--o{ CuttingRecordFabric : "contains"
    FabricVariant ||--o{ CuttingRecordFabric : "used in"
    
    CuttingRecordFabric ||--o{ DailySewingRecord : "sewn daily"
    
    CuttingRecord ||--|| FinishedProduct : "produces"
    FinishedProduct ||--o{ ProductImage : "has images"
    
    FinishedProduct ||--o{ PackingSession : "packed in"
    FinishedProduct ||--|| PackingInventory : "tracked in"
    
    Shop ||--o{ Order : "places"
    CustomUser ||--o{ Order : "created by"
    
    Order ||--o{ OrderItem : "contains"
    FinishedProduct ||--o{ OrderItem : "ordered as"
    
    Order ||--o{ Payment : "paid through"
