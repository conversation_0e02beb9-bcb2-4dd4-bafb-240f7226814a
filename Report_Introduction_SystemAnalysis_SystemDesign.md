# Pri Fashion Management System - Report Documentation

## Chapter 1: Introduction

### 1.1 Description about the Business Organization and the Business Area Chosen

Pri Fashion is a garment manufacturing business that specializes in the production and distribution of fashion apparel. The organization operates in the highly competitive fashion industry, focusing on efficient production processes, quality control, and streamlined order management to serve retail shops and distributors.

The business area chosen for this system development project encompasses the complete garment manufacturing lifecycle, from raw material procurement to finished product delivery. This includes supplier management, fabric inventory control, production planning, cutting operations, sewing processes, quality assurance, packing operations, order processing, and sales management.

The fashion industry is characterized by:
- Seasonal demand fluctuations
- Complex inventory management requirements
- Multiple stakeholder coordination
- Quality control challenges
- Time-sensitive order fulfillment
- Cost optimization pressures

### 1.2 Business Process

The current business process at Pri Fashion involves several interconnected stages:

**1. Procurement and Inventory Management:**
- Supplier registration and management
- Fabric procurement from multiple suppliers
- Inventory tracking with color variants and quantities
- Stock level monitoring and reorder management

**2. Production Planning and Cutting:**
- Production planning based on orders and forecasts
- Fabric cutting operations with size breakdowns (XS, S, M, L, XL)
- Cutting record maintenance for traceability
- Waste management and optimization

**3. Manufacturing Process:**
- Daily sewing operations tracking
- Production progress monitoring
- Quality control and defect tracking
- Finished product approval workflow

**4. Packing and Inventory:**
- Packing session management (6-packs, 12-packs, individual items)
- Finished goods inventory tracking
- Stock availability for sales

**5. Sales and Order Management:**
- Shop registration and management
- Order creation and processing
- Order status tracking (draft, submitted, approved, delivered, invoiced)
- Payment processing and invoice generation
- Delivery coordination

### 1.3 Problem Definition

The current manual and semi-automated processes at Pri Fashion face several critical challenges:

**1. Data Fragmentation:**
- Information scattered across multiple spreadsheets and documents
- Lack of real-time data synchronization
- Difficulty in tracking relationships between processes

**2. Inventory Management Issues:**
- Manual stock tracking leading to inaccuracies
- Difficulty in tracking fabric usage and availability
- Challenges in maintaining optimal stock levels

**3. Production Monitoring Challenges:**
- Limited visibility into production progress
- Difficulty in tracking defects and quality issues
- Inefficient resource allocation

**4. Order Processing Inefficiencies:**
- Manual order processing leading to delays
- Lack of real-time stock validation during order creation
- Difficulty in tracking order status and delivery

**5. Reporting and Analytics Limitations:**
- Time-consuming manual report generation
- Limited analytical insights for decision making
- Difficulty in identifying trends and patterns

**6. Role-based Access Control:**
- Lack of proper user role management
- Security concerns with data access
- Difficulty in maintaining audit trails

### 1.4 Aims and Objectives

**Overall Objective:**
To develop a comprehensive fashion garment management system that digitizes and streamlines all business processes from procurement to delivery, providing real-time visibility, improved efficiency, and enhanced decision-making capabilities.

**Specific Objectives:**

1. **Supplier and Inventory Management:**
   - Implement digital supplier registration and management
   - Develop real-time fabric inventory tracking system
   - Create automated stock level monitoring and alerts

2. **Production Management:**
   - Digitize cutting record management with traceability
   - Implement daily sewing progress tracking
   - Develop quality control and defect management system

3. **Order Processing:**
   - Create streamlined order management workflow
   - Implement real-time stock validation
   - Develop automated invoice generation

4. **User Management:**
   - Implement role-based access control (Owner, Inventory Manager, Sales Team, Order Coordinator)
   - Ensure data security and audit trails
   - Provide user-friendly interfaces for each role

5. **Reporting and Analytics:**
   - Generate real-time reports and dashboards
   - Provide analytical insights for business decisions
   - Implement data visualization for key metrics

6. **System Integration:**
   - Develop desktop application for easy access
   - Ensure cross-platform compatibility
   - Implement responsive design for mobile access

### 1.5 Scope with Clear Boundaries

**Included in Scope:**

1. **User Management Module:**
   - User registration and authentication
   - Role-based access control (4 user roles)
   - JWT token-based security

2. **Supplier Management Module:**
   - Supplier registration and profile management
   - Contact information and address management
   - Supplier-fabric relationship tracking

3. **Fabric Inventory Module:**
   - Fabric definition and variant management
   - Color-wise inventory tracking
   - Real-time stock level monitoring
   - Fabric usage tracking

4. **Production Management Module:**
   - Cutting record management
   - Daily sewing record tracking
   - Finished product approval workflow
   - Quality control and defect tracking

5. **Packing Module:**
   - Packing session management
   - Inventory tracking for packed goods
   - Multiple packing formats (6-pack, 12-pack, individual)

6. **Order Management Module:**
   - Shop registration and management
   - Order creation and processing
   - Order status workflow management
   - Payment tracking and invoice generation

7. **Reporting Module:**
   - Real-time dashboards
   - Production reports
   - Sales analytics
   - Inventory reports

8. **Desktop Application:**
   - Electron-based desktop wrapper
   - Automatic server startup
   - Professional desktop interface

**Excluded from Scope:**

1. **Financial Accounting:**
   - Complete accounting system
   - Tax calculations
   - Financial statement generation

2. **Human Resource Management:**
   - Employee management
   - Payroll processing
   - Attendance tracking

3. **External Integrations:**
   - Third-party payment gateways
   - External logistics systems
   - ERP system integrations

4. **Advanced Analytics:**
   - Machine learning predictions
   - Advanced forecasting algorithms
   - Business intelligence tools

### 1.6 Organisation of the Dissertation

This report is organized into seven main chapters:

**Chapter 1 - Introduction:** Provides background information about Pri Fashion, business processes, problem definition, objectives, and scope of the project.

**Chapter 2 - System Analysis:** Details the current system analysis using Object-Oriented Methodology (OOM), including use case diagrams, activity diagrams, class diagrams, and requirements specification.

**Chapter 3 - System Design:** Presents the proposed system design with detailed use case descriptions, activity diagrams, class diagrams, sequence diagrams, and database design.

**Chapter 4 - Development:** Discusses the implementation approach, programming languages, frameworks, third-party components, and development methodologies used.

**Chapter 5 - Testing:** Describes the testing strategy, test cases, quality assurance measures, and system reliability assessment.

**Chapter 6 - Implementation:** Covers the installation procedures, user guides, backup strategies, and security measures.

**Chapter 7 - Evaluation & Conclusion:** Evaluates the degree of objectives met, system usability, user feedback, limitations, and future enhancement possibilities.

---

## Chapter 2: System Analysis

### 2.1 User Requirements Using Use-Case Diagrams and Use-Case Descriptions (Current System)

The current system at Pri Fashion involves manual and semi-automated processes across different user roles. The following use cases represent the current business operations:

**Primary Actors:**
- Owner
- Inventory Manager  
- Sales Team
- Order Coordinator

**Current System Use Cases:**

**UC1: Manage Suppliers (Inventory Manager)**
- **Description:** Manually maintain supplier information in spreadsheets
- **Current Process:** Paper-based forms and Excel sheets
- **Issues:** Data duplication, version control problems

**UC2: Track Fabric Inventory (Inventory Manager)**
- **Description:** Monitor fabric stock levels manually
- **Current Process:** Physical counting and manual record keeping
- **Issues:** Inaccurate stock levels, delayed updates

**UC3: Record Cutting Operations (Inventory Manager)**
- **Description:** Document fabric cutting activities
- **Current Process:** Paper forms and manual calculations
- **Issues:** Calculation errors, lost records

**UC4: Monitor Production (Owner, Inventory Manager)**
- **Description:** Track daily sewing progress
- **Current Process:** Manual reporting and verbal updates
- **Issues:** Delayed information, incomplete data

**UC5: Process Orders (Sales Team, Order Coordinator)**
- **Description:** Handle customer orders manually
- **Current Process:** Phone calls, paper forms, manual calculations
- **Issues:** Order errors, stock validation problems

**UC6: Generate Reports (Owner)**
- **Description:** Create business reports manually
- **Current Process:** Data compilation from multiple sources
- **Issues:** Time-consuming, prone to errors

### 2.2 Activity Diagrams for Current System Functionality

**Activity Diagram 1: Current Order Processing**

```
Start → Receive Order Call → Write Order Details → Check Stock Manually → 
Calculate Pricing → Create Invoice → Process Payment → Update Records → End
```

**Swim Lanes:**
- **Sales Team:** Receive calls, write details, calculate pricing
- **Inventory Manager:** Check stock manually
- **Order Coordinator:** Create invoice, process payment
- **Owner:** Approve orders, review reports

**Issues Identified:**
- Manual stock checking causes delays
- No real-time inventory validation
- Multiple handoffs increase error probability
- No automated calculations

**Activity Diagram 2: Current Production Monitoring**

```
Start → Plan Production → Cut Fabrics → Record Cutting → Sewing Operations → 
Record Daily Progress → Quality Check → Pack Products → Update Inventory → End
```

**Issues Identified:**
- Manual record keeping at each stage
- No real-time progress tracking
- Difficulty in identifying bottlenecks
- Limited quality control documentation

### 2.3 Entity Classes from Verb-Noun Analysis

**Analysis of Current System Processes:**

**Nouns Identified:**
- Supplier, Fabric, Color, Inventory, Cutting Record, Sewing Record, Product, Order, Shop, Payment, User, Role

**Verbs Identified:**
- Register, Track, Cut, Sew, Pack, Order, Pay, Approve, Report, Manage

**Entity Classes Identified:**

1. **Supplier**
   - Attributes: ID, Name, Address, Contact Number
   - Responsibilities: Store supplier information

2. **Fabric**
   - Attributes: Name, Color, Quantity, Price, Supplier
   - Responsibilities: Track fabric inventory

3. **CuttingRecord**
   - Attributes: Date, Fabric Used, Quantities by Size
   - Responsibilities: Document cutting operations

4. **SewingRecord**
   - Attributes: Date, Progress, Defects
   - Responsibilities: Track production progress

5. **Product**
   - Attributes: Name, Price, Quantity, Status
   - Responsibilities: Manage finished products

6. **Order**
   - Attributes: Date, Shop, Items, Status, Total
   - Responsibilities: Process customer orders

7. **Shop**
   - Attributes: Name, Address, Contact
   - Responsibilities: Store customer information

8. **User**
   - Attributes: Username, Role, Permissions
   - Responsibilities: System access control

### 2.4 Current System Class Diagram

**Class Relationships:**

```
User ←→ Role (Many-to-One)
Supplier ←→ Fabric (One-to-Many)
Fabric ←→ CuttingRecord (Many-to-Many)
CuttingRecord ←→ SewingRecord (One-to-Many)
SewingRecord ←→ Product (Many-to-One)
Product ←→ Order (Many-to-Many)
Shop ←→ Order (One-to-Many)
Order ←→ Payment (One-to-Many)
```

**Current System Limitations:**
- No proper relationship management
- Data inconsistency issues
- Limited validation rules
- No audit trail capabilities

### 2.5 Software Requirement Specification

**Functional Requirements:**

**FR1: User Management**
- The system shall support user registration and authentication
- The system shall implement role-based access control
- The system shall maintain user session management

**FR2: Supplier Management**
- The system shall allow supplier registration
- The system shall maintain supplier contact information
- The system shall track supplier-fabric relationships

**FR3: Inventory Management**
- The system shall track fabric inventory in real-time
- The system shall support color-wise fabric variants
- The system shall provide stock level alerts

**FR4: Production Management**
- The system shall record cutting operations
- The system shall track daily sewing progress
- The system shall manage finished product approval

**FR5: Order Management**
- The system shall support order creation and processing
- The system shall validate stock availability
- The system shall generate invoices automatically

**Non-Functional Requirements:**

**NFR1: Performance**
- Response time shall be less than 2 seconds
- Report generation shall complete within 3 seconds
- System shall support concurrent users

**NFR2: Security**
- Data shall be encrypted in transit and at rest
- User authentication shall use JWT tokens
- Role-based access control shall be enforced

**NFR3: Usability**
- Interface shall be user-friendly and intuitive
- System shall be responsive for mobile devices
- Help documentation shall be available

**NFR4: Reliability**
- System uptime shall be 99.9%
- Automated daily backups shall be performed
- Data integrity shall be maintained

### 2.6 Complete Business System Options (BSOs)

**BSO 1: Web-Based System Only**
- **Description:** Pure web application accessible through browsers
- **Advantages:** Platform independent, easy deployment, automatic updates
- **Disadvantages:** Requires internet connection, limited offline capabilities
- **Cost:** Low development cost, minimal infrastructure requirements
- **Timeline:** 3-4 months development

**BSO 2: Desktop Application Only**
- **Description:** Standalone desktop application with local database
- **Advantages:** Offline capabilities, better performance, no internet dependency
- **Disadvantages:** Platform specific, manual updates, limited accessibility
- **Cost:** Medium development cost, higher maintenance
- **Timeline:** 4-5 months development

**BSO 3: Hybrid Desktop-Web System (Selected)**
- **Description:** Desktop application wrapping web technologies with local server
- **Advantages:** Best of both worlds, offline capabilities, easy deployment
- **Disadvantages:** Slightly complex architecture, larger application size
- **Cost:** Medium development cost, balanced maintenance
- **Timeline:** 4-5 months development

### 2.7 Cost Benefit Analysis

**Development Costs:**
- Developer time: 4 months × $2000/month = $8,000
- Software licenses and tools: $500
- Testing and deployment: $1,000
- **Total Development Cost: $9,500**

**Operational Benefits (Annual):**
- Time savings (5 hours/day × $20/hour × 250 days): $25,000
- Reduced errors and rework: $5,000
- Improved inventory management: $10,000
- Better customer service: $8,000
- **Total Annual Benefits: $48,000**

**Return on Investment:**
- **ROI = (Annual Benefits - Annual Costs) / Development Cost**
- **ROI = ($48,000 - $2,000) / $9,500 = 484%**
- **Payback Period: 2.5 months**

### 2.8 Selected BSO with Sound Justification

**Selected Option: BSO 3 - Hybrid Desktop-Web System**

**Justification:**

1. **Technical Advantages:**
   - Combines web technology flexibility with desktop application benefits
   - Electron framework provides cross-platform compatibility
   - Local server ensures offline functionality
   - Automatic port management prevents conflicts

2. **Business Benefits:**
   - Easy deployment through single executable file
   - No complex server setup required
   - Familiar desktop application experience
   - Automatic startup and shutdown

3. **User Experience:**
   - Professional desktop interface with taskbar integration
   - No browser dependency for daily operations
   - Consistent performance regardless of internet connectivity
   - Simplified user training and adoption

4. **Maintenance Advantages:**
   - Web-based updates through application restart
   - Centralized codebase for both frontend and backend
   - Standard web development tools and practices
   - Easy debugging and troubleshooting

5. **Scalability:**
   - Can be easily converted to full web application if needed
   - Supports future cloud deployment options
   - Modular architecture allows feature additions
   - Database can be migrated to cloud when required

This hybrid approach provides Pri Fashion with a robust, user-friendly solution that addresses current needs while maintaining flexibility for future growth and technological changes.

---

## Chapter 3: System Design

### 3.1 User Requirements for Proposed System Using Use-Case Diagrams and Use-Case Descriptions

The proposed Pri Fashion Management System addresses the limitations of the current manual system by providing a comprehensive digital solution. The following use cases represent the enhanced functionality:

**Enhanced Use Cases for Proposed System:**

**UC1: Authenticate User**
- **Actor:** All Users
- **Description:** Users log into the system using credentials and receive role-based access
- **Preconditions:** User account exists in the system
- **Main Flow:**
  1. User enters username and password
  2. System validates credentials
  3. System generates JWT token
  4. System redirects to role-specific dashboard
- **Postconditions:** User is authenticated and has access to authorized features

**UC2: Manage Suppliers (Enhanced)**
- **Actor:** Inventory Manager
- **Description:** Digital supplier management with validation and relationship tracking
- **Preconditions:** User is authenticated as Inventory Manager
- **Main Flow:**
  1. Navigate to supplier management
  2. Add/edit supplier information
  3. System validates Sri Lankan phone number format
  4. System prevents deletion if supplier has associated fabrics
  5. System saves supplier data
- **Postconditions:** Supplier information is stored with data integrity

**UC3: Manage Fabric Inventory (Enhanced)**
- **Actor:** Inventory Manager
- **Description:** Real-time fabric inventory management with color variants
- **Preconditions:** Suppliers exist in the system
- **Main Flow:**
  1. Create fabric definition with supplier
  2. Add color variants with quantities and pricing
  3. System automatically calculates available yards
  4. System provides real-time stock level display
  5. System generates low stock alerts
- **Postconditions:** Fabric inventory is accurately tracked

**UC4: Record Cutting Operations (Enhanced)**
- **Actor:** Inventory Manager
- **Description:** Digital cutting record with automatic inventory updates
- **Preconditions:** Fabric variants exist with sufficient stock
- **Main Flow:**
  1. Create cutting record with product name
  2. Select fabric variants and specify usage
  3. Enter size breakdown (XS, S, M, L, XL)
  4. System validates fabric availability
  5. System automatically updates fabric inventory
  6. System generates cutting record PDF
- **Postconditions:** Cutting operation is recorded and inventory is updated

**UC5: Track Daily Sewing Progress (Enhanced)**
- **Actor:** Inventory Manager
- **Description:** Digital tracking of daily sewing operations
- **Preconditions:** Cutting records exist
- **Main Flow:**
  1. Select cutting record for sewing update
  2. Enter daily progress by size
  3. Record defect counts
  4. System aggregates total sewn quantities
  5. System updates finished product status
- **Postconditions:** Sewing progress is tracked and aggregated

**UC6: Approve Finished Products (Enhanced)**
- **Actor:** Owner
- **Description:** Product approval workflow with pricing and image management
- **Preconditions:** Products have completed sewing
- **Main Flow:**
  1. Review finished product details
  2. Set manufacture and selling prices
  3. Upload product images (1-10 images)
  4. Add product notes
  5. Approve product for sales
- **Postconditions:** Product is approved and available for orders

**UC7: Manage Packing Operations (Enhanced)**
- **Actor:** Inventory Manager
- **Description:** Track packing sessions and inventory
- **Preconditions:** Approved finished products exist
- **Main Flow:**
  1. Create packing session
  2. Specify pack types (6-pack, 12-pack, individual)
  3. System updates packing inventory
  4. System calculates available quantities
  5. Generate packing reports
- **Postconditions:** Packing inventory is updated

**UC8: Process Orders (Enhanced)**
- **Actor:** Sales Team, Order Coordinator
- **Description:** Streamlined order processing with real-time validation
- **Preconditions:** Shops and products exist
- **Main Flow:**
  1. Create new order for shop
  2. Add products with quantities
  3. System validates stock availability in real-time
  4. System calculates total amount
  5. Submit order for approval
  6. Generate invoice upon approval
- **Postconditions:** Order is processed with accurate stock validation

**UC9: Manage Shop Information (Enhanced)**
- **Actor:** Sales Team, Owner
- **Description:** Digital shop management with location mapping
- **Preconditions:** User has appropriate permissions
- **Main Flow:**
  1. Register new shop
  2. Enter contact and address information
  3. Add GPS coordinates for mapping
  4. System validates contact number format
  5. Save shop information
- **Postconditions:** Shop is registered and available for orders

**UC10: Generate Reports and Analytics (Enhanced)**
- **Actor:** Owner, Sales Team
- **Description:** Real-time reports and interactive dashboards
- **Preconditions:** Data exists in the system
- **Main Flow:**
  1. Select report type and parameters
  2. System generates real-time data
  3. Display interactive charts and graphs
  4. Export reports to PDF/CSV
  5. Schedule automated reports
- **Postconditions:** Reports are generated and available

### 3.2 Activity Diagrams for Proposed System with System Column

**Activity Diagram 1: Enhanced Order Processing**

**Swim Lanes:**
- **Sales Team:** Order initiation and customer communication
- **System:** Validation, calculations, and data management
- **Order Coordinator:** Order approval and invoice generation
- **Inventory Manager:** Stock management

**Flow:**
```
Sales Team: Receive Order Request → Create Order in System
↓
System: Validate Shop Information → Check Real-time Stock Availability
↓
System: Calculate Pricing → Generate Order Summary
↓
Sales Team: Review Order → Submit for Approval
↓
Order Coordinator: Review Order → Approve/Reject
↓
System: Generate Invoice → Update Inventory → Send Notifications
↓
Order Coordinator: Process Payment → Mark as Delivered
↓
System: Update Order Status → Generate Delivery Reports
```

**System Enhancements:**
- Real-time stock validation prevents overselling
- Automatic pricing calculations reduce errors
- Workflow management ensures proper approvals
- Automated notifications keep stakeholders informed

**Activity Diagram 2: Enhanced Production Workflow**

**Swim Lanes:**
- **Inventory Manager:** Production operations
- **System:** Data processing and validation
- **Owner:** Approval and oversight

**Flow:**
```
Inventory Manager: Plan Production → Create Cutting Record
↓
System: Validate Fabric Availability → Calculate Requirements
↓
Inventory Manager: Execute Cutting → Record Actual Usage
↓
System: Update Fabric Inventory → Create Sewing Tasks
↓
Inventory Manager: Record Daily Sewing Progress
↓
System: Aggregate Progress → Calculate Completion Status
↓
System: Notify When Ready for Approval
↓
Owner: Review Product → Set Pricing → Approve
↓
System: Update Product Status → Make Available for Orders
```

### 3.3 Entity, Boundary, and Control Classes for Proposed System

**Entity Classes:**

1. **CustomUser**
   - **Attributes:** username, email, password, role, created_at
   - **Methods:** authenticate(), get_permissions(), update_profile()
   - **Responsibilities:** Store user information and handle authentication

2. **Role**
   - **Attributes:** name, permissions
   - **Methods:** add_permission(), remove_permission()
   - **Responsibilities:** Define user roles and permissions

3. **Supplier**
   - **Attributes:** supplier_id, name, address, tel_no
   - **Methods:** validate_phone(), get_fabrics()
   - **Responsibilities:** Store supplier information

4. **FabricDefinition**
   - **Attributes:** fabric_name, supplier, date_added
   - **Methods:** add_variant(), get_total_stock()
   - **Responsibilities:** Define fabric types

5. **FabricVariant**
   - **Attributes:** fabric_definition, color, total_yard, available_yard, price_per_yard
   - **Methods:** update_stock(), calculate_value(), validate_availability()
   - **Responsibilities:** Track specific fabric variants

6. **CuttingRecord**
   - **Attributes:** cutting_date, description, product_name
   - **Methods:** calculate_total_usage(), validate_requirements()
   - **Responsibilities:** Record cutting operations

7. **CuttingRecordFabric**
   - **Attributes:** cutting_record, fabric_variant, yard_usage, xs, s, m, l, xl
   - **Methods:** validate_stock(), update_inventory()
   - **Responsibilities:** Track fabric usage in cutting

8. **DailySewingRecord**
   - **Attributes:** cutting_record_fabric, date, xs, s, m, l, xl, damage_count
   - **Methods:** calculate_progress(), validate_quantities()
   - **Responsibilities:** Track daily sewing progress

9. **FinishedProduct**
   - **Attributes:** cutting_record, manufacture_price, selling_price, approval_date, is_provisional
   - **Methods:** calculate_total_sewn(), approve_product(), set_pricing()
   - **Responsibilities:** Manage finished products

10. **PackingSession**
    - **Attributes:** finished_product, date, number_of_6_packs, number_of_12_packs, extra_items
    - **Methods:** calculate_total_packed(), update_inventory()
    - **Responsibilities:** Record packing operations

11. **Shop**
    - **Attributes:** name, address, contact_number, district, latitude, longitude
    - **Methods:** validate_contact(), get_orders(), calculate_distance()
    - **Responsibilities:** Store customer shop information

12. **Order**
    - **Attributes:** shop, placed_by, created_at, status, approval_date, invoice_number
    - **Methods:** calculate_total(), approve_order(), generate_invoice()
    - **Responsibilities:** Manage customer orders

13. **OrderItem**
    - **Attributes:** order, finished_product, quantity, unit_price
    - **Methods:** calculate_line_total(), validate_stock()
    - **Responsibilities:** Track individual order items

**Boundary Classes (Interface Classes):**

1. **LoginInterface**
   - **Responsibilities:** Handle user authentication UI
   - **Methods:** display_login_form(), validate_input(), show_errors()

2. **DashboardInterface**
   - **Responsibilities:** Display role-specific dashboards
   - **Methods:** render_dashboard(), update_notifications(), show_metrics()

3. **SupplierManagementInterface**
   - **Responsibilities:** Supplier CRUD operations UI
   - **Methods:** display_supplier_form(), validate_phone_format(), show_supplier_list()

4. **InventoryInterface**
   - **Responsibilities:** Fabric inventory management UI
   - **Methods:** display_fabric_grid(), show_stock_alerts(), render_color_picker()

5. **ProductionInterface**
   - **Responsibilities:** Production tracking UI
   - **Methods:** display_cutting_form(), show_sewing_progress(), render_approval_form()

6. **OrderInterface**
   - **Responsibilities:** Order management UI
   - **Methods:** display_order_form(), validate_quantities(), show_invoice()

7. **ReportInterface**
   - **Responsibilities:** Reports and analytics UI
   - **Methods:** render_charts(), export_data(), schedule_reports()

**Control Classes:**

1. **AuthenticationController**
   - **Responsibilities:** Handle user authentication and authorization
   - **Methods:** login(), logout(), validate_token(), check_permissions()

2. **SupplierController**
   - **Responsibilities:** Manage supplier operations
   - **Methods:** create_supplier(), update_supplier(), delete_supplier(), validate_dependencies()

3. **InventoryController**
   - **Responsibilities:** Control fabric inventory operations
   - **Methods:** add_fabric(), update_stock(), check_availability(), generate_alerts()

4. **ProductionController**
   - **Responsibilities:** Manage production workflow
   - **Methods:** create_cutting_record(), update_sewing_progress(), approve_product()

5. **OrderController**
   - **Responsibilities:** Handle order processing
   - **Methods:** create_order(), validate_stock(), approve_order(), generate_invoice()

6. **ReportController**
   - **Responsibilities:** Generate reports and analytics
   - **Methods:** generate_report(), export_data(), calculate_metrics()

### 3.4 Class Diagram Communication

**Key Relationships:**

```
CustomUser ←→ Role (Many-to-One)
Supplier ←→ FabricDefinition (One-to-Many)
FabricDefinition ←→ FabricVariant (One-to-Many)
FabricVariant ←→ CuttingRecordFabric (One-to-Many)
CuttingRecord ←→ CuttingRecordFabric (One-to-Many)
CuttingRecordFabric ←→ DailySewingRecord (One-to-Many)
CuttingRecord ←→ FinishedProduct (One-to-One)
FinishedProduct ←→ PackingSession (One-to-Many)
FinishedProduct ←→ OrderItem (One-to-Many)
Shop ←→ Order (One-to-Many)
Order ←→ OrderItem (One-to-Many)
CustomUser ←→ Order (Many-to-One) [placed_by]
```

**Communication Patterns:**

1. **Authentication Flow:**
   LoginInterface → AuthenticationController → CustomUser → Role

2. **Inventory Management Flow:**
   InventoryInterface → InventoryController → FabricVariant → FabricDefinition → Supplier

3. **Production Flow:**
   ProductionInterface → ProductionController → CuttingRecord → CuttingRecordFabric → DailySewingRecord → FinishedProduct

4. **Order Processing Flow:**
   OrderInterface → OrderController → Order → OrderItem → FinishedProduct

### 3.5 Sequence Diagrams for Use Case Realization

**Sequence Diagram 1: User Authentication**

```
Actor: User
Boundary: LoginInterface
Control: AuthenticationController
Entity: CustomUser, Role

User → LoginInterface: enter_credentials(username, password)
LoginInterface → AuthenticationController: authenticate(username, password)
AuthenticationController → CustomUser: validate_credentials(username, password)
CustomUser → AuthenticationController: user_object
AuthenticationController → Role: get_permissions(user.role)
Role → AuthenticationController: permissions_list
AuthenticationController → LoginInterface: jwt_token, user_data
LoginInterface → User: redirect_to_dashboard(role_specific)
```

**Sequence Diagram 2: Create Order Process**

```
Actor: SalesTeam
Boundary: OrderInterface
Control: OrderController, InventoryController
Entity: Order, OrderItem, FinishedProduct, Shop

SalesTeam → OrderInterface: create_new_order()
OrderInterface → OrderController: initialize_order(shop_id)
OrderController → Shop: validate_shop(shop_id)
Shop → OrderController: shop_details
OrderController → OrderInterface: order_form
SalesTeam → OrderInterface: add_product(product_id, quantity)
OrderInterface → InventoryController: check_availability(product_id, quantity)
InventoryController → FinishedProduct: get_available_quantity(product_id)
FinishedProduct → InventoryController: available_qty
InventoryController → OrderInterface: validation_result
OrderInterface → OrderController: create_order_item(product_id, quantity)
OrderController → OrderItem: save()
OrderItem → OrderController: confirmation
OrderController → OrderInterface: order_summary
OrderInterface → SalesTeam: display_order_summary()
```

**Sequence Diagram 3: Fabric Cutting Process**

```
Actor: InventoryManager
Boundary: ProductionInterface
Control: ProductionController, InventoryController
Entity: CuttingRecord, CuttingRecordFabric, FabricVariant

InventoryManager → ProductionInterface: create_cutting_record()
ProductionInterface → ProductionController: initialize_cutting()
ProductionController → ProductionInterface: cutting_form
InventoryManager → ProductionInterface: add_fabric(fabric_id, usage, sizes)
ProductionInterface → InventoryController: validate_fabric_availability(fabric_id, usage)
InventoryController → FabricVariant: check_available_yard(fabric_id)
FabricVariant → InventoryController: available_quantity
InventoryController → ProductionInterface: validation_result
ProductionInterface → ProductionController: create_cutting_record_fabric(details)
ProductionController → CuttingRecordFabric: save()
CuttingRecordFabric → FabricVariant: update_available_yard(usage)
FabricVariant → CuttingRecordFabric: confirmation
CuttingRecordFabric → ProductionController: success
ProductionController → ProductionInterface: cutting_record_created
ProductionInterface → InventoryManager: display_success_message()
```

### 3.6 State Chart Diagrams for Class Behavior

## **State Chart Diagram 1: Order Class Behavior**

```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Submitted : submit()
    Draft --> [*] : cancel()

    Submitted --> Approved : approve()
    Submitted --> Draft : reject()

    Approved --> Delivered : deliver()
    Approved --> Draft : cancel()

    Delivered --> Paid : process_payment()
    Delivered --> PartiallyPaid : partial_payment()

    PartiallyPaid --> Paid : complete_payment()
    PartiallyPaid --> PaymentDue : set_due_date()

    PaymentDue --> Paid : process_payment()

    Paid --> [*]

    state Draft {
        [*] --> Initializing
        Initializing --> EditingItems : add_items()
        EditingItems --> ValidatingStock : validate()
        ValidatingStock --> EditingItems : validation_failed()
        ValidatingStock --> ReadyToSubmit : validation_passed()
        ReadyToSubmit --> [*] : submit()
    }

    state Approved {
        [*] --> GeneratingInvoice
        GeneratingInvoice --> PreparingDelivery : invoice_generated()
        PreparingDelivery --> ReadyForDelivery : preparation_complete()
        ReadyForDelivery --> [*] : deliver()
    }
```

**Order State Descriptions:**

**Draft State:**
- **Entry Actions:** Initialize order object, set default values
- **Internal Activities:** Add/remove order items, modify quantities, validate stock availability
- **Exit Actions:** Lock order items, generate order number
- **Transitions:**
  - submit() → Submitted (when validation passes)
  - cancel() → [*] (terminate order)

**Submitted State:**
- **Entry Actions:** Lock order for editing, notify approval team
- **Internal Activities:** Await owner/manager approval, review order details
- **Exit Actions:** Set approval status and timestamp
- **Transitions:**
  - approve() → Approved (owner approval)
  - reject() → Draft (return for modifications)

**Approved State:**
- **Entry Actions:** Generate invoice number, reserve inventory
- **Internal Activities:** Prepare delivery documentation, coordinate logistics
- **Exit Actions:** Update delivery status, create delivery record
- **Transitions:**
  - deliver() → Delivered (delivery completed)
  - cancel() → Draft (exceptional cancellation)

**Delivered State:**
- **Entry Actions:** Update delivery timestamp, notify payment team
- **Internal Activities:** Await payment processing, track payment status
- **Exit Actions:** Update payment records, generate receipts
- **Transitions:**
  - process_payment() → Paid (full payment received)
  - partial_payment() → Partially Paid (partial payment received)

**Partially Paid State:**
- **Entry Actions:** Record partial payment amount and date
- **Internal Activities:** Track remaining balance, send payment reminders
- **Exit Actions:** Update final payment status
- **Transitions:**
  - complete_payment() → Paid (remaining balance paid)
  - set_due_date() → Payment Due (establish payment deadline)

**Payment Due State:**
- **Entry Actions:** Set payment due date, activate reminder system
- **Internal Activities:** Send payment reminders, track overdue status
- **Exit Actions:** Clear due date, update payment status
- **Transitions:**
  - process_payment() → Paid (payment received)

**Paid State:**
- **Entry Actions:** Generate final receipt, update financial records
- **Internal Activities:** Archive order, update customer history
- **Exit Actions:** Complete order lifecycle
- **Transitions:** None (final state)

Provisional:
- Entry: Ready for owner review
- Activities: Await pricing and approval
- Exit: Owner decision
- Transitions: approve() → Approved, reject() → In Progress

Approved:
- Entry: Set pricing and images
- Activities: Available for orders
- Exit: Stock depletion or modification
- Transitions: stock_available() → Available, modify() → Provisional

Available:
- Entry: Ready for sale
- Activities: Process orders
- Exit: Stock status change
- Transitions: out_of_stock() → Out of Stock, restock() → Available

Out of Stock:
- Entry: No available inventory
- Activities: Await restocking
- Exit: New stock arrival
- Transitions: restock() → Available
```

**State Chart 3: Fabric Variant Inventory**

```
States: Available → Low Stock → Out of Stock → Discontinued

Available:
- Entry: Sufficient stock level
- Activities: Normal operations
- Exit: Stock reduction
- Transitions: low_threshold() → Low Stock, depleted() → Out of Stock

Low Stock:
- Entry: Below minimum threshold
- Activities: Generate alerts
- Exit: Restock or depletion
- Transitions: restock() → Available, depleted() → Out of Stock

Out of Stock:
- Entry: Zero available quantity
- Activities: Block new cutting operations
- Exit: Restock or discontinue
- Transitions: restock() → Available, discontinue() → Discontinued

Discontinued:
- Entry: No longer available
- Activities: Archive records
- Exit: None (final state)
```

### 3.7 Programming Language Selection and Properties

**Selected Technologies:**

**Backend: Django (Python)**
- **Properties Required:**
  - Object-oriented programming support
  - Robust ORM for database operations
  - RESTful API development capabilities
  - Built-in authentication and authorization
  - Extensive third-party package ecosystem
  - Strong security features

**Frontend: React (JavaScript)**
- **Properties Required:**
  - Component-based architecture
  - Virtual DOM for performance
  - Rich ecosystem of UI libraries
  - State management capabilities
  - Responsive design support
  - Cross-browser compatibility

**Desktop Wrapper: Electron (JavaScript/Node.js)**
- **Properties Required:**
  - Cross-platform desktop application development
  - Web technology integration
  - Native OS integration
  - Automatic updater support
  - Package management capabilities

**Database: MySQL**
- **Properties Required:**
  - ACID compliance
  - Relational data modeling
  - Transaction support
  - Scalability and performance
  - Backup and recovery features
  - Wide industry adoption

**Justification for Language Selection:**

1. **Python/Django:**
   - Rapid development capabilities
   - Excellent documentation and community support
   - Built-in admin interface for development
   - Strong security features out of the box
   - Extensive libraries for business logic implementation

2. **JavaScript/React:**
   - Modern, component-based UI development
   - Large ecosystem of reusable components
   - Excellent performance with virtual DOM
   - Strong community and continuous updates
   - Easy integration with backend APIs

3. **Electron:**
   - Enables desktop application using web technologies
   - Single codebase for multiple platforms
   - Native OS integration capabilities
   - Simplified deployment and distribution

### 3.8 Normalized Database Design

**Database Schema:**

**Table: auth_user (Django built-in, extended)**
- id (PK, AutoField)
- username (CharField, unique)
- email (EmailField)
- password (CharField, hashed)
- first_name (CharField)
- last_name (CharField)
- is_active (BooleanField)
- date_joined (DateTimeField)

**Table: authentication_role**
- id (PK, AutoField)
- name (CharField, unique)

**Table: authentication_customuser**
- user_ptr_id (PK, OneToOneField to auth_user)
- role_id (FK to authentication_role)

**Table: fabric_supplier**
- supplier_id (PK, AutoField)
- name (CharField)
- address (TextField)
- tel_no (CharField)

**Table: fabric_fabricdefinition**
- id (PK, AutoField)
- fabric_name (CharField)
- supplier_id (FK to fabric_supplier)
- date_added (DateField)

**Table: fabric_fabricvariant**
- id (PK, AutoField)
- fabric_definition_id (FK to fabric_fabricdefinition)
- color (CharField)
- color_name (CharField)
- total_yard (FloatField)
- available_yard (FloatField)
- price_per_yard (FloatField)

**Table: cutting_cuttingrecord**
- id (PK, AutoField)
- cutting_date (DateField)
- description (TextField)
- product_name (CharField)

**Table: cutting_cuttingrecordfabric**
- id (PK, AutoField)
- cutting_record_id (FK to cutting_cuttingrecord)
- fabric_variant_id (FK to fabric_fabricvariant)
- yard_usage (DecimalField)
- xs (IntegerField)
- s (IntegerField)
- m (IntegerField)
- l (IntegerField)
- xl (IntegerField)

**Table: sewing_dailysewingrecord**
- id (PK, AutoField)
- cutting_record_fabric_id (FK to cutting_cuttingrecordfabric)
- date (DateField)
- xs (IntegerField)
- s (IntegerField)
- m (IntegerField)
- l (IntegerField)
- xl (IntegerField)
- damage_count (IntegerField)

**Table: finished_product_finishedproduct**
- id (PK, AutoField)
- cutting_record_id (OneToOneField to cutting_cuttingrecord)
- total_sewn_xs (IntegerField)
- total_sewn_s (IntegerField)
- total_sewn_m (IntegerField)
- total_sewn_l (IntegerField)
- total_sewn_xl (IntegerField)
- manufacture_price (FloatField)
- selling_price (FloatField)
- approval_date (DateField)
- is_provisional (BooleanField)
- available_quantity (IntegerField)
- product_image (ImageField)
- notes (TextField)

**Table: order_shop**
- id (PK, AutoField)
- name (CharField)
- address (TextField)
- contact_number (CharField)
- district (CharField)
- latitude (FloatField)
- longitude (FloatField)

**Table: order_order**
- id (PK, AutoField)
- shop_id (FK to order_shop)
- placed_by_id (FK to authentication_customuser)
- created_at (DateTimeField)
- status (CharField)
- approval_date (DateTimeField)
- invoice_number (CharField)
- delivery_date (DateTimeField)
- delivered_items_count (PositiveIntegerField)
- delivery_notes (TextField)

**Table: order_orderitem**
- id (PK, AutoField)
- order_id (FK to order_order)
- finished_product_id (FK to finished_product_finishedproduct)
- quantity (PositiveIntegerField)
- unit_price (DecimalField)

**Normalization Analysis:**

**First Normal Form (1NF):** ✓
- All tables have atomic values
- No repeating groups
- Each row is unique

**Second Normal Form (2NF):** ✓
- All non-key attributes are fully functionally dependent on primary keys
- No partial dependencies

**Third Normal Form (3NF):** ✓
- No transitive dependencies
- All non-key attributes depend only on primary keys

**Database Constraints:**
- Foreign key constraints maintain referential integrity
- Check constraints ensure data validity (e.g., positive quantities)
- Unique constraints prevent duplicate entries
- Not null constraints ensure required data

### 3.9 Data Capturing Interfaces and Report Layouts

**Interface Design Principles:**
- Responsive design for multiple screen sizes
- Consistent navigation and branding
- Role-based interface customization
- Real-time data validation
- User-friendly error messaging

**Key Interface Layouts:**

**1. Login Interface:**
- Clean, centered login form
- Company logo and branding
- Remember me functionality
- Password reset option
- Role-based redirection

**2. Dashboard Interfaces:**
- **Owner Dashboard:** Complete system overview, analytics, approval workflows
- **Inventory Manager Dashboard:** Production metrics, stock levels, cutting/sewing operations
- **Sales Team Dashboard:** Order management, shop information, sales analytics
- **Order Coordinator Dashboard:** Order processing, delivery tracking, payment management

**3. Data Entry Forms:**
- **Supplier Form:** Contact validation, address management
- **Fabric Form:** Color picker, stock management, pricing
- **Cutting Record Form:** Multi-fabric selection, size breakdown, usage calculation
- **Order Form:** Real-time stock validation, automatic pricing, invoice preview

**Report Layouts:**

**1. Production Reports:**
- Cutting record summaries with fabric usage
- Daily sewing progress reports
- Finished product approval status
- Quality control and defect analysis

**2. Inventory Reports:**
- Current stock levels by fabric and color
- Low stock alerts and reorder recommendations
- Fabric usage trends and forecasting
- Supplier performance analysis

**3. Sales Reports:**
- Order status summaries
- Sales performance by shop and product
- Revenue analysis and trends
- Customer order history

**4. Financial Reports:**
- Invoice generation and tracking
- Payment status reports
- Profit margin analysis
- Cost breakdown by product

All reports include:
- Company logo and branding
- Date ranges and filtering options
- Export capabilities (PDF, CSV)
- Interactive charts and graphs
- Print-friendly layouts

---

## Chapter 4: Development

### 4.1 State Chart Diagrams to Describe the Behaviour of All Classes

**State Chart Diagram 1: CustomUser Class Behavior**

```
States: Inactive → Active → Authenticated → Authorized → Logged Out

Inactive:
- Entry: User account created but not activated
- Activities: Await email verification
- Exit: Account activation
- Transitions: activate() → Active

Active:
- Entry: Account is verified and enabled
- Activities: Can attempt login
- Exit: Login attempt
- Transitions: login() → Authenticated, deactivate() → Inactive

Authenticated:
- Entry: Valid credentials provided
- Activities: JWT token generated, session started
- Exit: Role verification
- Transitions: authorize() → Authorized, logout() → Logged Out

Authorized:
- Entry: Role permissions verified
- Activities: Access role-specific features
- Exit: Session end or logout
- Transitions: logout() → Logged Out, session_expire() → Active

Logged Out:
- Entry: User session terminated
- Activities: Clear authentication data
- Exit: New login attempt
- Transitions: login() → Authenticated
```

**State Chart Diagram 2: FabricVariant Class Behavior**

```
States: Created → Available → Reserved → Low Stock → Out of Stock → Discontinued

Created:
- Entry: New fabric variant added to system
- Activities: Initial stock setup, pricing configuration
- Exit: Stock validation complete
- Transitions: validate_stock() → Available

Available:
- Entry: Sufficient stock for operations
- Activities: Normal cutting operations, stock monitoring
- Exit: Stock level changes
- Transitions:
  - reserve_stock() → Reserved
  - low_threshold_reached() → Low Stock
  - stock_depleted() → Out of Stock

Reserved:
- Entry: Stock allocated for specific cutting operation
- Activities: Temporary hold on stock
- Exit: Cutting operation completion
- Transitions:
  - complete_cutting() → Available (if stock remains)
  - complete_cutting() → Low Stock (if below threshold)
  - cancel_reservation() → Available

Low Stock:
- Entry: Stock below minimum threshold
- Activities: Generate alerts, restrict large operations
- Exit: Restocking or depletion
- Transitions:
  - restock() → Available
  - stock_depleted() → Out of Stock

Out of Stock:
- Entry: Zero available quantity
- Activities: Block new cutting operations, await restocking
- Exit: Restocking or discontinuation
- Transitions:
  - restock() → Available
  - discontinue() → Discontinued

Discontinued:
- Entry: Fabric variant no longer available
- Activities: Archive records, prevent new operations
- Exit: None (final state)
```

**State Chart Diagram 3: CuttingRecord Class Behavior**

```
States: Draft → Validated → In Progress → Completed → Archived

Draft:
- Entry: New cutting record created
- Activities: Add fabric variants, specify quantities
- Exit: Validation request
- Transitions: validate() → Validated, cancel() → Archived

Validated:
- Entry: All fabric requirements validated
- Activities: Stock reserved, ready for cutting
- Exit: Cutting operation starts
- Transitions: start_cutting() → In Progress, modify() → Draft

In Progress:
- Entry: Cutting operation commenced
- Activities: Update progress, record actual usage
- Exit: Cutting completion
- Transitions: complete() → Completed, cancel() → Draft

Completed:
- Entry: All cutting operations finished
- Activities: Generate reports, update inventory
- Exit: Archive or sewing start
- Transitions:
  - start_sewing() → Completed (remains completed)
  - archive() → Archived

Archived:
- Entry: Record archived for historical purposes
- Activities: Read-only access, reporting
- Exit: None (final state)
```

**State Chart Diagram 4: FinishedProduct Class Behavior**

```
States: In Production → Provisional → Approved → Available → Sold Out → Discontinued

In Production:
- Entry: Created from completed cutting record
- Activities: Accumulate sewing progress, track defects
- Exit: Production completion
- Transitions: production_complete() → Provisional

Provisional:
- Entry: Ready for owner review and pricing
- Activities: Await owner approval, pricing setup
- Exit: Owner decision
- Transitions:
  - approve() → Approved
  - reject() → In Production
  - modify() → In Production

Approved:
- Entry: Owner approved with pricing set
- Activities: Available for order creation
- Exit: Stock status change
- Transitions:
  - stock_available() → Available
  - no_stock() → Sold Out

Available:
- Entry: Stock available for orders
- Activities: Process orders, update inventory
- Exit: Stock depletion or modification
- Transitions:
  - sold_out() → Sold Out
  - modify_pricing() → Provisional
  - discontinue() → Discontinued

Sold Out:
- Entry: No available inventory
- Activities: Block new orders, await restocking
- Exit: Restocking or discontinuation
- Transitions:
  - restock() → Available
  - discontinue() → Discontinued

Discontinued:
- Entry: Product no longer available
- Activities: Archive records, complete existing orders
- Exit: None (final state)
```

**State Chart Diagram 5: Order Class Behavior**

```
States: Draft → Submitted → Approved → In Delivery → Delivered → Invoiced → Completed

Draft:
- Entry: New order created
- Activities: Add/modify items, calculate totals
- Exit: Order submission
- Transitions: submit() → Submitted, cancel() → Completed

Submitted:
- Entry: Order submitted for approval
- Activities: Await coordinator approval
- Exit: Approval decision
- Transitions:
  - approve() → Approved
  - reject() → Draft
  - cancel() → Completed

Approved:
- Entry: Order approved for processing
- Activities: Prepare items, generate invoice
- Exit: Delivery initiation
- Transitions:
  - start_delivery() → In Delivery
  - cancel() → Draft

In Delivery:
- Entry: Items dispatched to customer
- Activities: Track delivery progress
- Exit: Delivery completion
- Transitions:
  - deliver() → Delivered
  - delivery_failed() → Approved

Delivered:
- Entry: Items delivered to customer
- Activities: Await payment processing
- Exit: Payment completion
- Transitions: process_payment() → Invoiced

Invoiced:
- Entry: Payment processed and invoice generated
- Activities: Final documentation
- Exit: Order completion
- Transitions: complete() → Completed

Completed:
- Entry: Order fully processed
- Activities: Archive order, generate reports
- Exit: None (final state)
```

### 4.2 Programming Language Properties Required and Selection

**Programming Language Properties Required for Class Diagram Implementation:**

**1. Object-Oriented Programming Support:**
- **Encapsulation:** Ability to bundle data and methods within classes
- **Inheritance:** Support for class hierarchies and code reuse
- **Polymorphism:** Method overriding and dynamic method dispatch
- **Abstraction:** Abstract classes and interfaces for design patterns

**2. Database Integration:**
- **Object-Relational Mapping (ORM):** Automatic mapping between objects and database tables
- **Transaction Support:** ACID compliance for data integrity
- **Connection Pooling:** Efficient database connection management
- **Migration Support:** Schema versioning and updates

**3. Web Framework Capabilities:**
- **RESTful API Development:** HTTP method handling and resource routing
- **Authentication and Authorization:** User management and role-based access
- **Session Management:** Stateful user interactions
- **Template Engine:** Dynamic content generation

**4. Security Features:**
- **Input Validation:** Protection against injection attacks
- **CSRF Protection:** Cross-site request forgery prevention
- **Password Hashing:** Secure password storage
- **JWT Token Support:** Stateless authentication

**5. Concurrency and Performance:**
- **Multi-threading:** Concurrent request handling
- **Asynchronous Processing:** Non-blocking operations
- **Caching Support:** Performance optimization
- **Load Balancing:** Scalability features

**Selected Programming Languages and Justification:**

**Backend: Python with Django Framework**

**Properties Satisfied:**
- **Object-Oriented:** Full OOP support with classes, inheritance, and polymorphism
- **Django ORM:** Powerful object-relational mapping with automatic SQL generation
- **Security:** Built-in protection against common web vulnerabilities
- **Scalability:** Supports horizontal and vertical scaling
- **Community:** Large ecosystem and extensive documentation

**Code Example - Model Definition:**
```python
class FabricVariant(models.Model):
    fabric_definition = models.ForeignKey(FabricDefinition, on_delete=models.CASCADE)
    color = models.CharField(max_length=50)
    total_yard = models.FloatField()
    available_yard = models.FloatField()

    def reserve_stock(self, quantity):
        if self.available_yard >= quantity:
            self.available_yard -= quantity
            self.save()
            return True
        return False

    def get_state(self):
        if self.available_yard <= 0:
            return "Out of Stock"
        elif self.available_yard < self.minimum_threshold:
            return "Low Stock"
        return "Available"
```

**Frontend: JavaScript with React Framework**

**Properties Satisfied:**
- **Component-Based Architecture:** Reusable UI components
- **State Management:** Efficient data flow and updates
- **Virtual DOM:** Performance optimization
- **Event Handling:** Interactive user interfaces
- **Ecosystem:** Rich library ecosystem for UI components

**Code Example - Component Definition:**
```javascript
class FabricVariantComponent extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            variant: null,
            status: 'Available'
        };
    }

    updateStock = (newQuantity) => {
        this.setState(prevState => ({
            variant: {
                ...prevState.variant,
                available_yard: newQuantity
            },
            status: this.calculateStatus(newQuantity)
        }));
    }

    calculateStatus = (quantity) => {
        if (quantity <= 0) return 'Out of Stock';
        if (quantity < this.props.minThreshold) return 'Low Stock';
        return 'Available';
    }
}
```

**Desktop Wrapper: Electron (Node.js)**

**Properties Satisfied:**
- **Cross-Platform:** Single codebase for Windows, macOS, Linux
- **Native Integration:** Access to OS features and file system
- **Web Technology Integration:** Reuse of existing web application
- **Automatic Updates:** Built-in update mechanisms

### 4.3 Data Structures and Algorithms

**Data Structures Used:**

**1. Hash Tables (Dictionaries/Objects):**
- **Usage:** User session management, caching frequently accessed data
- **Implementation:** Python dictionaries for configuration, JavaScript objects for state
- **Complexity:** O(1) average case for lookup, insertion, deletion

**Example:**
```python
# User permissions cache
user_permissions_cache = {
    'user_123': ['view_inventory', 'edit_fabric', 'create_cutting'],
    'user_456': ['view_orders', 'create_order', 'approve_order']
}
```

**2. Arrays/Lists:**
- **Usage:** Order items, fabric variants, size breakdowns
- **Implementation:** Python lists, JavaScript arrays
- **Complexity:** O(1) for access, O(n) for search

**Example:**
```python
# Size breakdown for cutting record
size_breakdown = [
    {'size': 'XS', 'quantity': 10},
    {'size': 'S', 'quantity': 25},
    {'size': 'M', 'quantity': 30},
    {'size': 'L', 'quantity': 20},
    {'size': 'XL', 'quantity': 15}
]
```

**3. Trees (Hierarchical Structures):**
- **Usage:** Role hierarchy, category organization
- **Implementation:** Nested objects/dictionaries
- **Complexity:** O(log n) for balanced trees

**Example:**
```python
# Role hierarchy
role_hierarchy = {
    'Owner': {
        'permissions': ['all'],
        'subordinates': ['Inventory Manager', 'Sales Team', 'Order Coordinator']
    },
    'Inventory Manager': {
        'permissions': ['manage_inventory', 'view_production'],
        'subordinates': []
    }
}
```

**4. Queues:**
- **Usage:** Order processing workflow, notification system
- **Implementation:** Python deque, JavaScript arrays with shift/push
- **Complexity:** O(1) for enqueue/dequeue operations

**Example:**
```python
from collections import deque

# Order processing queue
order_queue = deque()
order_queue.append(new_order)  # Add to queue
current_order = order_queue.popleft()  # Process next order
```

**Algorithms Implemented:**

**1. Stock Validation Algorithm:**
```python
def validate_stock_availability(order_items):
    """
    Validates if sufficient stock exists for all order items
    Time Complexity: O(n) where n is number of order items
    """
    for item in order_items:
        product = FinishedProduct.objects.get(id=item.product_id)
        available_qty = calculate_available_quantity(product)

        if available_qty < item.quantity:
            return False, f"Insufficient stock for {product.name}"

    return True, "Stock validation successful"

def calculate_available_quantity(product):
    """
    Calculates available quantity: total_sewn - total_packed - total_ordered
    Time Complexity: O(1) with database indexing
    """
    total_sewn = (product.total_sewn_xs + product.total_sewn_s +
                  product.total_sewn_m + product.total_sewn_l +
                  product.total_sewn_xl)

    total_packed = product.packing_sessions.aggregate(
        total=Sum(F('number_of_6_packs') * 6 +
                 F('number_of_12_packs') * 12 +
                 F('extra_items'))
    )['total'] or 0

    return total_sewn - total_packed
```

**2. Fabric Usage Optimization Algorithm:**
```python
def optimize_fabric_cutting(fabric_requirements):
    """
    Optimizes fabric cutting to minimize waste
    Uses First Fit Decreasing algorithm
    Time Complexity: O(n log n)
    """
    # Sort requirements by quantity (descending)
    sorted_requirements = sorted(fabric_requirements,
                               key=lambda x: x.total_quantity,
                               reverse=True)

    cutting_plan = []

    for requirement in sorted_requirements:
        best_fit_variant = find_best_fit_variant(requirement)
        if best_fit_variant:
            cutting_plan.append({
                'variant': best_fit_variant,
                'usage': requirement.yard_needed,
                'sizes': requirement.size_breakdown
            })

    return cutting_plan

def find_best_fit_variant(requirement):
    """
    Finds fabric variant with minimum waste
    Time Complexity: O(m) where m is number of variants
    """
    suitable_variants = FabricVariant.objects.filter(
        fabric_definition=requirement.fabric_definition,
        available_yard__gte=requirement.yard_needed
    )

    if not suitable_variants:
        return None

    # Select variant with minimum waste
    return min(suitable_variants,
              key=lambda v: v.available_yard - requirement.yard_needed)
```

**3. Order Priority Scheduling Algorithm:**
```python
def schedule_orders(pending_orders):
    """
    Schedules orders based on priority and delivery date
    Uses Shortest Job First with priority weighting
    Time Complexity: O(n log n)
    """
    def calculate_priority_score(order):
        # Higher score = higher priority
        base_score = 0

        # Delivery urgency (days until delivery)
        days_until_delivery = (order.requested_delivery_date - timezone.now().date()).days
        urgency_score = max(0, 10 - days_until_delivery)

        # Order value weight
        value_score = min(order.total_amount / 1000, 10)  # Cap at 10

        # Customer priority
        customer_score = order.shop.priority_level * 2

        return urgency_score + value_score + customer_score

    # Sort by priority score (descending)
    return sorted(pending_orders,
                 key=calculate_priority_score,
                 reverse=True)
```

**4. Inventory Reorder Algorithm:**
```python
def calculate_reorder_points():
    """
    Calculates when to reorder fabric based on usage patterns
    Uses Economic Order Quantity (EOQ) model
    Time Complexity: O(n) where n is number of fabric variants
    """
    import math

    reorder_recommendations = []

    for variant in FabricVariant.objects.all():
        # Calculate average daily usage
        usage_history = get_usage_history(variant, days=30)
        avg_daily_usage = sum(usage_history) / len(usage_history) if usage_history else 0

        # Lead time (days to receive new stock)
        lead_time = variant.fabric_definition.supplier.lead_time_days

        # Safety stock (buffer for demand variability)
        usage_variance = calculate_variance(usage_history)
        safety_stock = math.sqrt(lead_time * usage_variance) * 1.65  # 95% service level

        # Reorder point
        reorder_point = (avg_daily_usage * lead_time) + safety_stock

        if variant.available_yard <= reorder_point:
            # Calculate optimal order quantity (EOQ)
            annual_demand = avg_daily_usage * 365
            ordering_cost = 100  # Fixed cost per order
            holding_cost = variant.price_per_yard * 0.2  # 20% of item cost

            eoq = math.sqrt((2 * annual_demand * ordering_cost) / holding_cost)

            reorder_recommendations.append({
                'variant': variant,
                'current_stock': variant.available_yard,
                'reorder_point': reorder_point,
                'recommended_quantity': eoq,
                'urgency': 'High' if variant.available_yard < reorder_point * 0.5 else 'Medium'
            })

    return reorder_recommendations

### 4.4 Third Party Components / Libraries Used

**Backend Libraries (Python/Django):**

**1. Django Framework (v5.1.7)**
- **Purpose:** Web framework for rapid development
- **Features:** ORM, authentication, admin interface, security
- **Justification:** Provides robust foundation with built-in security and scalability
- **Usage:** Core framework for all backend functionality

**2. Django REST Framework (DRF)**
- **Purpose:** Building RESTful APIs
- **Features:** Serialization, authentication, permissions, browsable API
- **Justification:** Industry standard for Django API development
- **Usage:** All API endpoints for frontend communication

**3. djangorestframework-simplejwt**
- **Purpose:** JWT authentication for Django REST Framework
- **Features:** Token generation, refresh, blacklisting
- **Justification:** Stateless authentication suitable for SPA applications
- **Usage:** User authentication and authorization

**4. django-cors-headers**
- **Purpose:** Cross-Origin Resource Sharing (CORS) handling
- **Features:** Configurable CORS policies
- **Justification:** Required for React frontend to communicate with Django backend
- **Usage:** Enable cross-origin requests from frontend

**5. Pillow**
- **Purpose:** Python Imaging Library for image processing
- **Features:** Image manipulation, format conversion, thumbnail generation
- **Justification:** Required for Django ImageField and product image handling
- **Usage:** Product image upload and processing

**6. python-decouple**
- **Purpose:** Environment variable management
- **Features:** Configuration separation, type casting
- **Justification:** Secure configuration management and environment separation
- **Usage:** Database credentials, secret keys, debug settings

**7. mysqlclient**
- **Purpose:** MySQL database adapter for Python
- **Features:** High-performance MySQL connectivity
- **Justification:** Native MySQL support with better performance than pure Python drivers
- **Usage:** Database connectivity and operations

**Frontend Libraries (JavaScript/React):**

**1. React (v18.x)**
- **Purpose:** User interface library
- **Features:** Component-based architecture, virtual DOM, hooks
- **Justification:** Modern, efficient UI development with large ecosystem
- **Usage:** Core frontend framework

**2. React Router DOM**
- **Purpose:** Client-side routing for React applications
- **Features:** Declarative routing, nested routes, route protection
- **Justification:** Standard routing solution for React SPAs
- **Usage:** Navigation between different pages and role-based routing

**3. Axios**
- **Purpose:** HTTP client for API communication
- **Features:** Promise-based, request/response interceptors, automatic JSON parsing
- **Justification:** Reliable and feature-rich HTTP client with excellent error handling
- **Usage:** All API calls to Django backend

**4. Chart.js with react-chartjs-2**
- **Purpose:** Data visualization and charting
- **Features:** Interactive charts, responsive design, multiple chart types
- **Justification:** Comprehensive charting library with React integration
- **Usage:** Dashboard analytics, reports, and data visualization

**5. React Bootstrap**
- **Purpose:** UI component library
- **Features:** Pre-built responsive components, consistent styling
- **Justification:** Rapid UI development with professional appearance
- **Usage:** Forms, modals, navigation, layout components

**6. React Icons**
- **Purpose:** Icon library for React
- **Features:** Multiple icon sets, tree-shaking, consistent API
- **Justification:** Comprehensive icon collection with easy integration
- **Usage:** UI icons throughout the application

**7. React Toastify**
- **Purpose:** Notification system
- **Features:** Customizable toast notifications, positioning, animations
- **Justification:** User-friendly notification system for feedback
- **Usage:** Success/error messages, system notifications

**8. date-fns**
- **Purpose:** Date manipulation and formatting
- **Features:** Lightweight, modular, immutable date operations
- **Justification:** Modern alternative to moment.js with better performance
- **Usage:** Date formatting, calculations, and validations

**Desktop Application Libraries (Electron):**

**1. Electron**
- **Purpose:** Desktop application framework
- **Features:** Cross-platform, web technology integration, native APIs
- **Justification:** Enables desktop deployment using existing web technologies
- **Usage:** Desktop wrapper for the web application

**2. electron-builder**
- **Purpose:** Application packaging and distribution
- **Features:** Multi-platform builds, auto-updater, code signing
- **Justification:** Comprehensive build tool for Electron applications
- **Usage:** Creating distributable desktop application

**3. concurrently**
- **Purpose:** Run multiple commands concurrently
- **Features:** Parallel process execution, output management
- **Justification:** Simplifies running backend and frontend simultaneously
- **Usage:** Development and production startup scripts

**Development and Build Tools:**

**1. Node.js and npm**
- **Purpose:** JavaScript runtime and package management
- **Features:** Package management, script execution, dependency resolution
- **Justification:** Standard toolchain for JavaScript development
- **Usage:** Frontend development, build processes, dependency management

**2. Create React App**
- **Purpose:** React application scaffolding
- **Features:** Zero-configuration setup, build optimization, development server
- **Justification:** Industry standard for React project initialization
- **Usage:** Frontend project structure and build configuration

**3. Webpack (via Create React App)**
- **Purpose:** Module bundling and asset optimization
- **Features:** Code splitting, hot reloading, asset optimization
- **Justification:** Efficient bundling and optimization for production
- **Usage:** Frontend build process and optimization

**Database and Storage:**

**1. MySQL**
- **Purpose:** Relational database management system
- **Features:** ACID compliance, scalability, replication, backup
- **Justification:** Reliable, well-documented database with excellent Django support
- **Usage:** Primary data storage for all application data

**2. File System Storage**
- **Purpose:** Media file storage
- **Features:** Local file storage, directory organization
- **Justification:** Simple and reliable for single-server deployment
- **Usage:** Product images, generated reports, backup files

**Security Libraries:**

**1. bcrypt (via Django)**
- **Purpose:** Password hashing
- **Features:** Adaptive hashing, salt generation, timing attack resistance
- **Justification:** Industry standard for secure password storage
- **Usage:** User password hashing and verification

**2. cryptography (Django dependency)**
- **Purpose:** Cryptographic operations
- **Features:** Encryption, decryption, digital signatures
- **Justification:** Required for Django's security features
- **Usage:** Session security, CSRF protection, data encryption

**Utility Libraries:**

**1. python-dotenv**
- **Purpose:** Environment variable loading from .env files
- **Features:** Development environment configuration
- **Justification:** Simplifies development environment setup
- **Usage:** Loading development configuration

**2. whitenoise**
- **Purpose:** Static file serving for Django
- **Features:** Efficient static file serving, compression
- **Justification:** Simplifies deployment by serving static files from Django
- **Usage:** Serving CSS, JavaScript, and image files in production

**Testing Libraries:**

**1. Django Test Framework**
- **Purpose:** Backend testing
- **Features:** Unit tests, integration tests, test database
- **Justification:** Built-in testing framework with Django integration
- **Usage:** Backend API and model testing

**2. Jest (via Create React App)**
- **Purpose:** JavaScript testing framework
- **Features:** Unit tests, snapshot testing, mocking
- **Justification:** Standard testing framework for React applications
- **Usage:** Frontend component and utility testing

**Library Selection Criteria:**

**1. Maturity and Stability:**
- All selected libraries have stable releases and active maintenance
- Long-term support and backward compatibility considerations
- Proven track record in production environments

**2. Community Support:**
- Large user communities and extensive documentation
- Active development and regular updates
- Availability of tutorials and learning resources

**3. Performance:**
- Optimized for production use
- Minimal overhead and efficient resource utilization
- Scalability considerations for future growth

**4. Security:**
- Regular security updates and vulnerability patches
- Built-in security features and best practices
- Compliance with industry security standards

**5. Integration:**
- Seamless integration with chosen technology stack
- Compatible APIs and consistent development patterns
- Minimal configuration requirements

**6. Licensing:**
- Open-source licenses compatible with commercial use
- No licensing restrictions for distribution
- Cost-effective for long-term maintenance

This comprehensive selection of third-party components provides a robust foundation for the Pri Fashion Management System, ensuring reliability, security, and maintainability while enabling rapid development and deployment.
```
